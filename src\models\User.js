const mongoose = require("mongoose");
const bcrypt = require("bcrypt");

const userSchema = new mongoose.Schema({
  email: { type: String, required: true, unique: true },
  password: { type: String, required: true },
  role: {
    type: String,
    enum: ["user", "admin"],
    default: "user",
  },
  preferences: {
    location: { type: String },
    budget: { type: Number },
    rooms: { type: Number },
    propertyType: {
      type: String,
      enum: ["apartment", "house", "studio", "room", "any"],
    },
    minSize: { type: Number },
    maxSize: { type: Number },
    interior: {
      type: String,
      enum: ["kaal", "gestoffeerd", "gemeubileerd", "any"],
    },
    parking: { type: Boolean },
    balcony: { type: Boolean },
    garden: { type: Boolean },
    furnished: { type: Boolean },
    petsAllowed: { type: Boolean },
    smokingAllowed: { type: Boolean },
    studentFriendly: { type: <PERSON><PERSON><PERSON> },
    expatFriendly: { type: <PERSON><PERSON><PERSON> },
    commuteTime: { type: Number }, // in minutes
    preferredNeighborhoods: [{ type: String }],
    excludedNeighborhoods: [{ type: String }],
  },
  profile: {
    name: { type: String },
    income: { type: Number },
    occupation: { type: String },
    employmentType: {
      type: String,
      enum: ["full-time", "part-time", "student", "freelancer", "unemployed"],
    },
    contractType: {
      type: String,
      enum: ["permanent", "temporary", "student", "freelancer"],
    },
    moveInDate: { type: Date },
    leaseDuration: { type: Number }, // in months
    hasGuarantor: { type: Boolean },
    creditScore: { type: String, enum: ["excellent", "good", "fair", "poor"] },
    rentalHistory: {
      previousRentals: [{ type: String }],
      evictions: { type: Boolean, default: false },
      paymentIssues: { type: Boolean, default: false },
    },
  },
  aiSettings: {
    matchThreshold: { type: Number, default: 70 }, // minimum match score
    alertFrequency: {
      type: String,
      enum: ["immediate", "hourly", "daily"],
      default: "immediate",
    },
    preferredLanguage: {
      type: String,
      enum: ["dutch", "english"],
      default: "english",
    },
    includeMarketAnalysis: { type: Boolean, default: true },
    includeContractAnalysis: { type: Boolean, default: true },
    autoGenerateApplications: { type: Boolean, default: false },
    applicationTemplate: {
      type: String,
      enum: ["professional", "casual", "student", "expat"],
      default: "professional",
    },
  },
  createdAt: { type: Date, default: Date.now },
  lastActive: { type: Date, default: Date.now },
});

userSchema.pre("save", async function (next) {
  if (!this.isModified("password")) return next();
  this.password = await bcrypt.hash(this.password, 10);
  next();
});

module.exports = mongoose.model("User", userSchema);

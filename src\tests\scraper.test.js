const { scrapeFunda, getScrapingMetrics } = require("../services/scraper");
const mongoose = require("mongoose");
const config = require("../config/config");

// Mock data for testing
const mockListingData = {
  title: "Test Apartment",
  url: "https://www.funda.nl/detail/huur/amsterdam/appartement-test-1/12345/",
  location: "Amsterdam",
  propertyType: "appartement",
  price: "€ 2.000 per maand",
  size: "85 m²",
  bedrooms: 2,
  dateAdded: new Date(),
};

describe("Funda Scraper Tests", () => {
  let mongoConnection;

  beforeAll(async () => {
    // Connect to test database
    const testDbUri = config.mongoURI.replace(/\/[^\/]*$/, "/test_zakmakelaar");
    mongoConnection = await mongoose.connect(testDbUri);
    console.log("Connected to test database");
  });

  afterAll(async () => {
    // Clean up and close connection
    await mongoose.connection.close();
    console.log("Test database connection closed");
  });

  describe("Data Extraction Tests", () => {
    test("should extract price from HTML content", () => {
      const htmlContent = `
        <div>
          <p>Rental price: € 1.500 per maand</p>
          <p>Size: 120 m²</p>
          <p>Bedrooms: 3 slaapkamers</p>
        </div>
      `;

      // Test price extraction patterns
      const pricePattern = /€\s*[\d.,]+\s*per\s*maand/gi;
      const priceMatch = htmlContent.match(pricePattern);

      expect(priceMatch).toBeTruthy();
      expect(priceMatch[0]).toBe("€ 1.500 per maand");
    });

    test("should extract size from HTML content", () => {
      const htmlContent = "Property size is 95 m² with garden";

      const sizePattern = /(\d+)\s*m²/i;
      const sizeMatch = htmlContent.match(sizePattern);

      expect(sizeMatch).toBeTruthy();
      expect(sizeMatch[1]).toBe("95");
    });

    test("should extract bedroom count from HTML content", () => {
      const htmlContent = "This property has 4 slaapkamers and 2 bathrooms";

      const bedroomPattern = /(\d+)\s*slaapkamer/i;
      const bedroomMatch = htmlContent.match(bedroomPattern);

      expect(bedroomMatch).toBeTruthy();
      expect(parseInt(bedroomMatch[1])).toBe(4);
    });

    test("should parse property type from URL", () => {
      const testUrls = [
        "https://www.funda.nl/detail/huur/amsterdam/huis-test-street-1/12345/",
        "https://www.funda.nl/detail/huur/amsterdam/appartement-test-street-1/12345/",
        "https://www.funda.nl/detail/huur/amsterdam/kamer-test-street-1/12345/",
      ];

      testUrls.forEach((url) => {
        if (url.includes("/huis-")) {
          expect(url).toContain("huis-");
        } else if (url.includes("/appartement-")) {
          expect(url).toContain("appartement-");
        } else if (url.includes("/kamer-")) {
          expect(url).toContain("kamer-");
        }
      });
    });

    test("should parse city from URL correctly", () => {
      const url =
        "https://www.funda.nl/detail/huur/den-haag/huis-test-street-1/12345/";
      const urlParts = url.split("/");
      const huurIndex = urlParts.indexOf("huur");

      if (huurIndex !== -1 && huurIndex + 1 < urlParts.length) {
        const city = urlParts[huurIndex + 1].replace(/-/g, " ");
        const formattedCity = city.replace(/\b\w/g, (l) => l.toUpperCase());

        expect(formattedCity).toBe("Den Haag");
      }
    });
  });

  describe("Error Handling Tests", () => {
    test("should handle invalid URLs gracefully", async () => {
      // This test would require mocking the browser/page functionality
      // For now, we'll test the URL validation logic
      const invalidUrls = [
        "",
        "not-a-url",
        "https://invalid-domain.com",
        null,
        undefined,
      ];

      invalidUrls.forEach((url) => {
        const isValidFundaUrl =
          url &&
          typeof url === "string" &&
          url.startsWith("https://www.funda.nl/");
        if (!isValidFundaUrl) {
          expect(isValidFundaUrl).toBeFalsy();
        }
      });
    });

    test("should handle missing price data", () => {
      const htmlWithoutPrice =
        "<div><p>No price information available</p></div>";

      const pricePattern = /€\s*[\d.,]+\s*per\s*maand/gi;
      const priceMatch = htmlWithoutPrice.match(pricePattern);

      expect(priceMatch).toBeNull();
    });

    test("should validate listing data structure", () => {
      const validListing = {
        title: "Test Property",
        url: "https://www.funda.nl/detail/huur/amsterdam/huis-test/12345/",
        location: "Amsterdam",
        propertyType: "huis",
        price: "€ 2.000 per maand",
        dateAdded: new Date(),
      };

      // Check required fields
      expect(validListing.title).toBeTruthy();
      expect(validListing.url).toBeTruthy();
      expect(validListing.location).toBeTruthy();
      expect(validListing.propertyType).toBeTruthy();
      expect(validListing.price).toBeTruthy();
      expect(validListing.dateAdded).toBeInstanceOf(Date);
    });
  });

  describe("Performance Tests", () => {
    test("should complete scraping within reasonable time", async () => {
      const startTime = Date.now();

      // Mock a quick scraping operation
      await new Promise((resolve) => setTimeout(resolve, 100));

      const endTime = Date.now();
      const duration = endTime - startTime;

      // Should complete within 5 minutes for a full scrape
      expect(duration).toBeLessThan(300000);
    });

    test("should handle concurrent requests properly", async () => {
      // Test that multiple scraping operations don't interfere
      const promises = [];

      for (let i = 0; i < 3; i++) {
        promises.push(
          new Promise((resolve) => {
            setTimeout(() => resolve(`result-${i}`), Math.random() * 100);
          })
        );
      }

      const results = await Promise.all(promises);
      expect(results).toHaveLength(3);
      expect(results).toContain("result-0");
      expect(results).toContain("result-1");
      expect(results).toContain("result-2");
    });
  });

  describe("Metrics Tests", () => {
    test("should track scraping metrics correctly", () => {
      const metrics = getScrapingMetrics();

      expect(metrics).toHaveProperty("totalScrapes");
      expect(metrics).toHaveProperty("successfulScrapes");
      expect(metrics).toHaveProperty("failedScrapes");
      expect(metrics).toHaveProperty("successRate");

      expect(typeof metrics.totalScrapes).toBe("number");
      expect(typeof metrics.successfulScrapes).toBe("number");
      expect(typeof metrics.failedScrapes).toBe("number");
      expect(typeof metrics.successRate).toBe("string");
    });
  });

  describe("Integration Tests", () => {
    // Note: These tests would require actual network access
    // In a production environment, you might want to mock these

    test("should validate Funda URL structure", () => {
      const validUrls = [
        "https://www.funda.nl/detail/huur/amsterdam/huis-test/12345/",
        "https://www.funda.nl/detail/huur/rotterdam/appartement-test/67890/",
        "https://www.funda.nl/detail/huur/utrecht/kamer-test/11111/",
      ];

      validUrls.forEach((url) => {
        expect(url).toMatch(
          /^https:\/\/www\.funda\.nl\/detail\/huur\/[^\/]+\/[^\/]+\/\d+\/$/
        );
      });
    });

    test("should handle different property types", () => {
      const propertyTypes = [
        "huis",
        "appartement",
        "kamer",
        "studio",
        "parkeergelegenheid",
      ];

      propertyTypes.forEach((type) => {
        expect([
          "huis",
          "appartement",
          "kamer",
          "studio",
          "parkeergelegenheid",
          "woning",
        ]).toContain(type);
      });
    });
  });
});

// Helper functions for testing
function createMockListing(overrides = {}) {
  return {
    ...mockListingData,
    ...overrides,
  };
}

function validateListingStructure(listing) {
  const requiredFields = [
    "title",
    "url",
    "location",
    "propertyType",
    "price",
    "dateAdded",
  ];
  return requiredFields.every(
    (field) => listing.hasOwnProperty(field) && listing[field] !== null
  );
}

module.exports = {
  createMockListing,
  validateListingStructure,
};

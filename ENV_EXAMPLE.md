# Environment Variables Configuration

Create a `.env` file in the root directory with the following variables:

## Database Configuration

```bash
MONGO_URI=mongodb://localhost:27017/zakmakelaar
REDIS_URL=redis://localhost:6379
```

## Server Configuration

```bash
PORT=3000
NODE_ENV=development
CORS_ORIGIN=http://localhost:3000
```

## JWT Configuration

```bash
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRES_IN=7d
```

## Rate Limiting

```bash
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000
```

## Scraping Configuration

```bash
SCRAPING_INTERVAL_MINUTES=5
SCRAPING_TIMEOUT_MS=60000
```

## Notification Services

```bash
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_WHATSAPP_FROM=whatsapp:+**********
```

## Cache Configuration

```bash
CACHE_LISTINGS_TTL=300
CACHE_USER_TTL=1800
```

## OpenRouter AI Configuration

```bash
# Get your API key from https://openrouter.ai/keys
OPENROUTER_API_KEY=your-openrouter-api-key

# Default model settings
OPENROUTER_DEFAULT_MODEL=openai/gpt-4o-mini
OPENROUTER_MAX_TOKENS=4000
OPENROUTER_TEMPERATURE=0.7

# AI Model Configuration (optional - will use defaults if not set)
OPENROUTER_ANALYSIS_MODEL=openai/gpt-4o-mini
OPENROUTER_MATCHING_MODEL=anthropic/claude-3-haiku
OPENROUTER_SUMMARIZATION_MODEL=meta-llama/llama-3.1-8b-instruct
OPENROUTER_TRANSLATION_MODEL=google/gemini-flash-1.5
```

## Getting OpenRouter API Key

1. Visit [OpenRouter](https://openrouter.ai/)
2. Sign up for an account
3. Go to [API Keys](https://openrouter.ai/keys)
4. Create a new API key
5. Add the key to your `.env` file

## Available AI Models

OpenRouter provides access to hundreds of AI models. Here are some recommended models for different tasks:

### Analysis & Complex Reasoning

- `openai/gpt-4o-mini` - Best for contract analysis and market insights
- `anthropic/claude-3-5-sonnet` - Excellent for detailed analysis

### Matching & Recommendations

- `anthropic/claude-3-haiku` - Fast and cost-effective for matching
- `meta-llama/llama-3.1-8b-instruct` - Good for preference matching

### Summarization

- `meta-llama/llama-3.1-8b-instruct` - Efficient for listing summaries
- `google/gemini-flash-1.5` - Good for content summarization

### Translation

- `google/gemini-flash-1.5` - Excellent for Dutch-English translation
- `openai/gpt-4o-mini` - High-quality translation with context

## Cost Optimization

OpenRouter allows you to choose different models based on your needs and budget:

- **High Performance**: Use GPT-4o-mini for critical analysis
- **Cost Effective**: Use Claude-3-Haiku for routine matching
- **Fast Processing**: Use Llama models for quick summaries
- **Translation**: Use Gemini Flash for language translation

## Testing AI Features

After setting up your environment variables, you can test the AI features:

```bash
# Test AI matching
curl -X POST http://localhost:3000/api/ai/match \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "listing": {
      "title": "Modern Apartment in Amsterdam",
      "price": "1500",
      "location": "Amsterdam",
      "size": "75",
      "rooms": "3",
      "propertyType": "apartment"
    },
    "userPreferences": {
      "location": "Amsterdam",
      "budget": 2000,
      "rooms": 2,
      "propertyType": "apartment"
    }
  }'
```

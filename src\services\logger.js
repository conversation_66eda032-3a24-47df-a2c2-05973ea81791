const winston = require("winston");
const DailyRotateFile = require("winston-daily-rotate-file");
const path = require("path");
const config = require("../config/config");

// Create logs directory if it doesn't exist
const fs = require("fs");
const logsDir = path.join(__dirname, "../../logs");
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for logs
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: "YYYY-MM-DD HH:mm:ss",
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// Console format for development
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: "HH:mm:ss",
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let msg = `${timestamp} [${level}]: ${message}`;
    if (Object.keys(meta).length > 0) {
      msg += ` ${JSON.stringify(meta)}`;
    }
    return msg;
  })
);

// Create logger instance
const logger = winston.createLogger({
  level: config.nodeEnv === "production" ? "info" : "debug",
  format: logFormat,
  defaultMeta: { service: "zakmakelaar-api" },
  transports: [
    // Error logs - separate file for errors only
    new DailyRotateFile({
      filename: path.join(logsDir, "error-%DATE%.log"),
      datePattern: "YYYY-MM-DD",
      level: "error",
      maxSize: "20m",
      maxFiles: "14d",
      zippedArchive: true,
    }),

    // Combined logs - all logs
    new DailyRotateFile({
      filename: path.join(logsDir, "combined-%DATE%.log"),
      datePattern: "YYYY-MM-DD",
      maxSize: "20m",
      maxFiles: "14d",
      zippedArchive: true,
    }),

    // Application logs - info and above
    new DailyRotateFile({
      filename: path.join(logsDir, "app-%DATE%.log"),
      datePattern: "YYYY-MM-DD",
      level: "info",
      maxSize: "20m",
      maxFiles: "30d",
      zippedArchive: true,
    }),
  ],
});

// Add console transport for development
if (config.nodeEnv !== "production") {
  logger.add(
    new winston.transports.Console({
      format: consoleFormat,
      level: "debug",
    })
  );
}

// Create specialized loggers for different components
const loggers = {
  // Main application logger
  app: logger,

  // Database operations logger
  db: logger.child({ component: "database" }),

  // API requests logger
  api: logger.child({ component: "api" }),

  // Scraping operations logger
  scraper: logger.child({ component: "scraper" }),

  // Cache operations logger
  cache: logger.child({ component: "cache" }),

  // Authentication logger
  auth: logger.child({ component: "auth" }),

  // Error logger for unhandled errors
  error: logger.child({ component: "error" }),
};

// Helper functions for common logging patterns
const logHelpers = {
  // Log API request
  logRequest: (req, res, responseTime) => {
    loggers.api.info("API Request", {
      method: req.method,
      url: req.originalUrl,
      ip: req.ip,
      userAgent: req.get("User-Agent"),
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userId: req.user ? req.user._id : null,
    });
  },

  // Log database operation
  logDbOperation: (operation, collection, duration, error = null) => {
    if (error) {
      loggers.db.error("Database Operation Failed", {
        operation,
        collection,
        duration: `${duration}ms`,
        error: error.message,
        stack: error.stack,
      });
    } else {
      loggers.db.info("Database Operation", {
        operation,
        collection,
        duration: `${duration}ms`,
      });
    }
  },

  // Log scraping operation
  logScraping: (
    site,
    status,
    listingsFound = 0,
    duration = 0,
    error = null
  ) => {
    if (error) {
      loggers.scraper.error("Scraping Failed", {
        site,
        status,
        duration: `${duration}ms`,
        error: error.message,
      });
    } else {
      loggers.scraper.info("Scraping Completed", {
        site,
        status,
        listingsFound,
        duration: `${duration}ms`,
      });
    }
  },

  // Log cache operation
  logCache: (operation, key, hit = null, error = null) => {
    if (error) {
      loggers.cache.error("Cache Operation Failed", {
        operation,
        key,
        error: error.message,
      });
    } else {
      loggers.cache.debug("Cache Operation", {
        operation,
        key,
        hit: hit !== null ? (hit ? "HIT" : "MISS") : null,
      });
    }
  },

  // Log authentication event
  logAuth: (
    event,
    userId = null,
    email = null,
    ip = null,
    success = true,
    error = null
  ) => {
    const logData = {
      event,
      userId,
      email,
      ip,
      success,
    };

    if (error) {
      logData.error = error.message;
      loggers.auth.warn("Authentication Event", logData);
    } else {
      loggers.auth.info("Authentication Event", logData);
    }
  },

  // Log performance metrics
  logPerformance: (operation, duration, metadata = {}) => {
    loggers.app.info("Performance Metric", {
      operation,
      duration: `${duration}ms`,
      ...metadata,
    });
  },

  /**
   * Log AI operations for monitoring and analytics
   */
  logAiOperation(operation, status, details = "") {
    const logData = {
      timestamp: new Date().toISOString(),
      level: "info",
      category: "ai_operation",
      operation,
      status,
      details,
      environment: config.nodeEnv,
    };

    if (status === "error") {
      logData.level = "error";
    }

    logger.info("AI Operation", logData);
  },

  /**
   * Log AI performance metrics
   */
  logAiPerformance(operation, duration, model, tokensUsed) {
    const logData = {
      timestamp: new Date().toISOString(),
      level: "info",
      category: "ai_performance",
      operation,
      duration: `${duration}ms`,
      model,
      tokensUsed,
      environment: config.nodeEnv,
    };

    logger.info("AI Performance", logData);
  },
};

// Express middleware for request logging
const requestLogger = (req, res, next) => {
  const start = Date.now();

  res.on("finish", () => {
    const duration = Date.now() - start;
    logHelpers.logRequest(req, res, duration);
  });

  next();
};

module.exports = {
  logger,
  loggers,
  logHelpers,
  requestLogger,
};

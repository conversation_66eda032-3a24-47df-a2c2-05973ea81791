const express = require("express");
const router = express.Router();
const auth = require("../middleware/auth");

// Debug logging
console.log("Loading agent routes...");

// Import controller with debug logging
let agentController;
try {
  agentController = require("../controllers/agentController");
  console.log(
    "Agent controller loaded successfully:",
    Object.keys(agentController)
  );
} catch (error) {
  console.error("Error loading agent controller:", error);
  throw error; // Re-throw to prevent silent failures
}

// Simple admin check middleware
const restrictToAdmin = (req, res, next) => {
  if (req.user && req.user.role === "admin") {
    return next();
  }
  res
    .status(403)
    .json({ status: "error", message: "Access denied. Admin role required." });
};

/**
 * @swagger
 * tags:
 *   name: Agent
 *   description: AI Agent management and monitoring
 */

/**
 * @swagger
 * /api/agent/status:
 *   get:
 *     summary: Get current agent status
 *     tags: [Agent]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Agent status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentStatus'
 */
router.get("/status", auth, agentController.getAgentStatus);

/**
 * @swagger
 * /api/agent/start:
 *   post:
 *     summary: Start the agent
 *     tags: [Agent]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Agent started successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentStatus'
 */
router.post("/start", auth, restrictToAdmin, agentController.startAgent);

/**
 * @swagger
 * /api/agent/stop:
 *   post:
 *     summary: Stop the agent
 *     tags: [Agent]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Agent stopped successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentStatus'
 */
router.post("/stop", auth, restrictToAdmin, agentController.stopAgent);

/**
 * @swagger
 * /api/agent/config:
 *   patch:
 *     summary: Update agent configuration
 *     tags: [Agent]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               config:
 *                 type: object
 *                 description: Configuration to update
 *     responses:
 *       200:
 *         description: Configuration updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentStatus'
 */
router.patch(
  "/config",
  auth,
  restrictToAdmin,
  agentController.updateAgentConfig
);

/**
 * @swagger
 * /api/agent/metrics:
 *   get:
 *     summary: Get agent metrics
 *     tags: [Agent]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Metrics retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/AgentMetrics'
 */
router.get("/metrics", auth, agentController.getAgentMetrics);

/**
 * @swagger
 * /api/agent/health:
 *   get:
 *     summary: Get agent health status
 *     tags: [Agent]
 *     responses:
 *       200:
 *         description: Health status retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   enum: [active, inactive, degraded]
 *                 uptime:
 *                   type: number
 *                 timestamp:
 *                   type: string
 *                 metrics:
 *                   type: object
 */
router.get("/health", agentController.healthCheck);

module.exports = router;

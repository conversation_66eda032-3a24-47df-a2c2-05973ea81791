const OpenAI = require("openai");
const config = require("../config/config");
const { logHelpers } = require("./logger");

class AIService {
  constructor() {
    this.openai = new OpenAI({
      apiKey: config.openRouter.apiKey,
      baseURL: config.openRouter.baseURL,
    });
    this.defaultModel = config.openRouter.defaultModel;
    this.maxTokens = config.openRouter.maxTokens;
    this.temperature = config.openRouter.temperature;
  }

  /**
   * Helper function to extract JSON from AI response
   */
  extractJSONFromResponse(response) {
    try {
      // First, try to parse as direct JSON
      return JSON.parse(response);
    } catch (error) {
      // If that fails, try to extract JSO<PERSON> from markdown code blocks
      const jsonMatch = response.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/);
      if (jsonMatch) {
        try {
          return JSON.parse(jsonMatch[1]);
        } catch (parseError) {
          console.error("Failed to parse JSON from markdown:", parseError);
          throw new Error("Invalid JSON response from AI");
        }
      }

      // If no markdown blocks, try to find JSON in the text
      const jsonRegex = /\{[\s\S]*\}/;
      const match = response.match(jsonRegex);
      if (match) {
        try {
          return JSON.parse(match[0]);
        } catch (parseError) {
          console.error("Failed to parse JSON from text:", parseError);
          throw new Error("Invalid JSON response from AI");
        }
      }

      throw new Error("No valid JSON found in AI response");
    }
  }

  /**
   * AI-powered listing matching based on user preferences
   */
  async matchListingToUser(listing, userPreferences) {
    try {
      const prompt = this.buildMatchingPrompt(listing, userPreferences);

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.matching,
        messages: [
          {
            role: "system",
            content:
              "You are an expert real estate agent specializing in Dutch rental properties. Analyze listings against user preferences and provide a detailed match score and reasoning. Always respond with valid JSON only, no markdown formatting.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: this.maxTokens,
        temperature: 0.3, // Lower temperature for more consistent matching
      });

      const result = this.extractJSONFromResponse(
        response.choices[0].message.content
      );
      logHelpers.logAiOperation("listing_matching", "success", result.score);

      return result;
    } catch (error) {
      logHelpers.logAiOperation("listing_matching", "error", error.message);
      throw error;
    }
  }

  /**
   * Contract analysis and legal guidance
   */
  async analyzeContract(contractText, language = "dutch") {
    try {
      const prompt = this.buildContractAnalysisPrompt(contractText, language);

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.analysis,
        messages: [
          {
            role: "system",
            content:
              "You are a Dutch real estate lawyer specializing in rental contracts. Analyze contracts for legal compliance, potential issues, and provide clear explanations. Always respond with valid JSON only, no markdown formatting.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: this.maxTokens,
        temperature: 0.2, // Very low temperature for legal analysis
      });

      const result = this.extractJSONFromResponse(
        response.choices[0].message.content
      );
      logHelpers.logAiOperation(
        "contract_analysis",
        "success",
        result.riskLevel
      );

      return result;
    } catch (error) {
      logHelpers.logAiOperation("contract_analysis", "error", error.message);
      throw error;
    }
  }

  /**
   * Generate personalized application messages
   */
  async generateApplicationMessage(
    listing,
    userProfile,
    template = "professional"
  ) {
    try {
      const prompt = this.buildApplicationPrompt(
        listing,
        userProfile,
        template
      );

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.analysis,
        messages: [
          {
            role: "system",
            content:
              "You are a professional real estate assistant. Generate personalized, compelling application messages for rental properties that highlight the applicant's strengths and suitability. Respond with the application message only, no JSON formatting.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: 1000,
        temperature: 0.7,
      });

      const message = response.choices[0].message.content;
      logHelpers.logAiOperation("application_generation", "success", template);

      return {
        message,
        template,
        generatedAt: new Date(),
      };
    } catch (error) {
      logHelpers.logAiOperation(
        "application_generation",
        "error",
        error.message
      );
      throw error;
    }
  }

  /**
   * Market analysis and price prediction
   */
  async analyzeMarketTrends(location, propertyType, historicalData) {
    try {
      const prompt = this.buildMarketAnalysisPrompt(
        location,
        propertyType,
        historicalData
      );

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.analysis,
        messages: [
          {
            role: "system",
            content:
              "You are a Dutch real estate market analyst. Analyze market trends, provide price predictions, and offer insights about rental market conditions. Always respond with valid JSON only, no markdown formatting.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: this.maxTokens,
        temperature: 0.4,
      });

      const result = this.extractJSONFromResponse(
        response.choices[0].message.content
      );
      logHelpers.logAiOperation("market_analysis", "success", location);

      return result;
    } catch (error) {
      logHelpers.logAiOperation("market_analysis", "error", error.message);
      throw error;
    }
  }

  /**
   * Smart listing summarization
   */
  async summarizeListing(listing, language = "english") {
    try {
      const prompt = this.buildSummarizationPrompt(listing, language);

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.summarization,
        messages: [
          {
            role: "system",
            content:
              "You are a real estate expert. Create concise, informative summaries of rental listings highlighting key features and benefits. Respond with the summary only, no JSON formatting.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: 500,
        temperature: 0.5,
      });

      const summary = response.choices[0].message.content;
      logHelpers.logAiOperation("listing_summarization", "success", language);

      return {
        summary,
        language,
        generatedAt: new Date(),
      };
    } catch (error) {
      logHelpers.logAiOperation(
        "listing_summarization",
        "error",
        error.message
      );
      throw error;
    }
  }

  /**
   * Translate listing content
   */
  async translateContent(content, fromLanguage, toLanguage) {
    try {
      const prompt = this.buildTranslationPrompt(
        content,
        fromLanguage,
        toLanguage
      );

      const response = await this.openai.chat.completions.create({
        model: config.openRouter.models.translation,
        messages: [
          {
            role: "system",
            content:
              "You are a professional translator specializing in real estate terminology. Translate content accurately while preserving technical terms and cultural context. Respond with the translation only, no JSON formatting.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        max_tokens: this.maxTokens,
        temperature: 0.3,
      });

      const translation = response.choices[0].message.content;
      logHelpers.logAiOperation(
        "content_translation",
        "success",
        `${fromLanguage}-${toLanguage}`
      );

      return {
        original: content,
        translation,
        fromLanguage,
        toLanguage,
        translatedAt: new Date(),
      };
    } catch (error) {
      logHelpers.logAiOperation("content_translation", "error", error.message);
      throw error;
    }
  }

  // Private helper methods for building prompts
  buildMatchingPrompt(listing, userPreferences) {
    return `
    Analyze this rental listing against user preferences and provide a detailed match assessment.

    LISTING:
    - Title: ${listing.title}
    - Price: ${listing.price}
    - Location: ${listing.location}
    - Size: ${listing.size}
    - Rooms: ${listing.rooms}
    - Property Type: ${listing.propertyType}
    - Interior: ${listing.interior}

    USER PREFERENCES:
    - Preferred Location: ${userPreferences.location || "Any"}
    - Budget Range: ${userPreferences.budget || "No limit"}
    - Preferred Rooms: ${userPreferences.rooms || "Any"}
    - Property Type: ${userPreferences.propertyType || "Any"}

    Please provide a JSON response with:
    {
      "score": 0-100,
      "matchReasoning": "Detailed explanation of why this listing matches or doesn't match",
      "keyHighlights": ["List of key features that match preferences"],
      "potentialConcerns": ["Any potential issues or concerns"],
      "recommendation": "strong_match|good_match|moderate_match|poor_match"
    }
    `;
  }

  buildContractAnalysisPrompt(contractText, language) {
    return `
    Analyze this Dutch rental contract for legal compliance and potential issues.

    CONTRACT TEXT:
    ${contractText}

    Please provide a JSON response with:
    {
      "riskLevel": "low|medium|high",
      "complianceScore": 0-100,
      "keyClauses": ["Important clauses identified"],
      "potentialIssues": ["List of potential legal issues"],
      "recommendations": ["Suggestions for improvement"],
      "summary": "Brief summary in ${language}",
      "legalAdvice": "Professional legal advice"
    }
    `;
  }

  buildApplicationPrompt(listing, userProfile, template) {
    return `
    Generate a ${template} application message for this rental property.

    PROPERTY:
    - Title: ${listing.title}
    - Location: ${listing.location}
    - Price: ${listing.price}

    APPLICANT PROFILE:
    - Name: ${userProfile.name}
    - Income: ${userProfile.income}
    - Occupation: ${userProfile.occupation || "Professional"}

    Generate a compelling, professional application message that highlights the applicant's suitability for this property.
    `;
  }

  buildMarketAnalysisPrompt(location, propertyType, historicalData) {
    return `
    Analyze the rental market trends for ${propertyType} in ${location}.

    HISTORICAL DATA:
    ${JSON.stringify(historicalData)}

    Please provide a JSON response with:
    {
      "marketTrend": "increasing|stable|decreasing",
      "pricePrediction": "Expected price movement",
      "demandLevel": "high|medium|low",
      "keyInsights": ["Important market insights"],
      "recommendations": ["Recommendations for renters"],
      "confidenceScore": 0-100
    }
    `;
  }

  buildSummarizationPrompt(listing, language) {
    return `
    Create a concise summary of this rental listing in ${language}.

    LISTING DETAILS:
    - Title: ${listing.title}
    - Price: ${listing.price}
    - Location: ${listing.location}
    - Size: ${listing.size}
    - Rooms: ${listing.rooms}
    - Property Type: ${listing.propertyType}

    Provide a 2-3 sentence summary highlighting the key features and benefits.
    `;
  }

  buildTranslationPrompt(content, fromLanguage, toLanguage) {
    return `
    Translate the following ${fromLanguage} real estate content to ${toLanguage}:

    CONTENT:
    ${content}

    Maintain professional real estate terminology and cultural context in the translation.
    `;
  }
}

module.exports = new AIService();
